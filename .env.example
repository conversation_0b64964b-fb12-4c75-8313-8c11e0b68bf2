# Node
NPM_TOKEN

# Migration
DATABASE_CLIENT
DATABASE_HOST
DATABASE_PORT
DATABASE_USER
DATABASE_PASSWORD
DATABASE_SCHEMA
DATABASE_SLAVES
DATABASE_SECURE_CONNECT
DATABASE_TLS_CERT
DATABASE_MIGRATION_TABLE_NAME

# Mysql
MYSQL_HOST
MYSQL_PORT
MYSQL_USER
MYSQL_PASSWORD
MYSQL_DATABASE
MYSQL_SLAVES

#Postgresql
POSTGRES_HOST
POSTGRES_PORT
POSTGRES_USER
POSTGRES_PASSWORD
POSTGRES_DATABASE
POSTGRES_SLAVES

# Stripe
STRIPE_SECRET_KEY
STRIPE_WEBHOOK_SIGNING_SECRET

# Auth
AUTHENTICATION_API_URL
SECRET_KEY_INTERNAL_API_AUTHENTICATION
PATH_JWT_PUBLIC

# Module connector
ORDER_MODULE_CONNECTION_TYPE
ORDER_MODULE_HOST
ORDER_MODULE_PATHNAME_ORDER_UPDATE