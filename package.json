{"name": "@cbidigital/payment-module", "version": "1.0.0", "description": "Payment module", "main": "index.js", "scripts": {"build": "node build.js && tsc --build tsconfig.json && tsc-alias", "debug": "nodemon --env=development --config nodemon.debug.json", "nodemon": "nodemon --config nodemon.debug.json", "bdev": "node build.js && tsc --build tsconfig.json", "start": "node dist/index.js", "start:dev": "nodemon --env=development --config nodemon.debug.json", "migrate:make": "knex migrate:make", "migrate:up": "knex migrate:up", "migrate:latest": "knex migrate:latest", "migrate:down": "knex migrate:down", "migrate:rollback": "knex migrate:rollback", "migrate:list": "knex migrate:list", "seed:make": "knex seed:make", "seed:run": "knex seed:run", "test": "jest --updateSnapshot --detect<PERSON><PERSON>Handles", "copy": "cp -r migrations/ dist/migrations && cp package.json dist/ && cp README.md dist/ && cp .npmrc dist/", "lint": "eslint --ext .js,.ts .", "lint:fix": "eslint --fix --ext .js,.ts .", "format": "prettier --write 'src/**/*.{js,ts}'", "lint:format": "prettier --write .", "package": "npm run build && node module.js"}, "keywords": [], "dependencies": {"@cbidigital/aqua-ddd": "^1.0.0-rc.9", "@heronjs/common": "3.4.10", "@heronjs/core": "3.5.10", "@heronjs/express": "3.1.16", "class-validator": "^0.14.2", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "moment": "^2.30.1", "pg": "^8.16.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.2", "tsc-alias": "^1.8.16", "zod": "^3.25.49"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.29", "eslint": "^9.28.0", "eslint-plugin-prettier": "^5.4.1", "fs-extra": "^11.3.0", "globals": "^16.2.0", "nodemon": "^3.1.10", "prettier": "^3.5.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1"}}