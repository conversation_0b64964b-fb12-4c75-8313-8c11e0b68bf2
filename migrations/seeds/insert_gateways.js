const TABLE_NAME = 'gateways';

const gateways = [
    {
        code: 'default',
        label: 'Default',
        desc: null,
        status: 'enabled',
        created_at: new Date(),
    },
    {
        code: 'stripe',
        label: 'Stripe',
        desc: null,
        status: 'enabled',
        created_at: new Date(),
    },
];

exports.seed = async function (knex) {
    for (const gateway of gateways) {
        const gatewayExist = await knex(TABLE_NAME).select('code').where({ code: gateway.code }).first();
        if (!gatewayExist) {
            await knex(TABLE_NAME).insert(gateway);
        }
    }
};
