const randomUUID = require('uuid').v4;

const TABLE_NAME = 'methods';

const methods = [
    {
        code: 'free',
        gateway_code: 'default',
        label: 'Free',
        desc: null,
        status: 'enabled',
        visibility: true,
        sort_order: 0,
        platform_exclusion: JSON.stringify([]),
        source_type_exclusion: JSON.stringify([]),
        created_at: new Date(),
    },
    {
        code: 'cod',
        gateway_code: 'default',
        label: 'Cash on delivery',
        desc: null,
        status: 'enabled',
        visibility: true,
        sort_order: 1,
        platform_exclusion: JSON.stringify([]),
        source_type_exclusion: JSON.stringify([]),
        created_at: new Date(),
    },
    {
        code: 'cards',
        gateway_code: 'stripe',
        label: 'Cards',
        desc: null,
        status: 'enabled',
        visibility: true,
        sort_order: 2,
        platform_exclusion: JSON.stringify([]),
        source_type_exclusion: JSON.stringify([]),
        created_at: new Date(),
    },
    {
        code: 'wallets',
        gateway_code: 'stripe',
        label: 'Wallets',
        desc: null,
        status: 'enabled',
        visibility: true,
        sort_order: 3,
        platform_exclusion: JSON.stringify([]),
        source_type_exclusion: JSON.stringify([]),
        created_at: new Date(),
    },
    {
        code: 'subscription',
        gateway_code: 'stripe',
        label: 'Subscription',
        desc: null,
        status: 'enabled',
        visibility: true,
        sort_order: 4,
        platform_exclusion: JSON.stringify([]),
        source_type_exclusion: JSON.stringify([]),
        created_at: new Date(),
    },
];

exports.seed = async function (knex) {
    for (const method of methods) {
        const gatewayExist = await knex('gateways')
            .select('code')
            .where({ code: method.gateway_code })
            .first();

        if (!gatewayExist) throw new Error(`Gateway with code ${method.gateway_code} is not exist!`);

        const methodExist = await knex(TABLE_NAME).select('code').where({ code: method.code }).first();

        if (!methodExist) {
            await knex(TABLE_NAME).insert(method);
        }
    }
};
