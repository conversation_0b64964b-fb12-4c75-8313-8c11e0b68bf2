const SCHEMA = 'payment';
const TABLE_NAME = 'tbl_gateway_customers';

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
    return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
        table.uuid('id').primary();
        table.string('gateway', 32).references('code').inTable(`${SCHEMA}.tbl_gateways`).notNullable();
        table.uuid('user_id').notNullable();
        table.string('gateway_customer_id').notNullable();
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at');

        table.unique(['gateway', 'user_id']);
    });
};

exports.down = async (knex) => {};
