const SCHEMA = 'payment';
const TABLE_NAME = 'tbl_webhook_events';

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
    return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
        table.uuid('id').primary();
        table.string('gateway', 50).references('code').inTable(`${SCHEMA}.tbl_gateways`).notNullable();
        table.string('reference_id', 50).notNullable();
        table.string('event_id').notNullable();
        table.string('event_type').notNullable();
        table.string('status', 20).notNullable();
        table.json('payload').notNullable();
        table.string('error_message');
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at');
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
