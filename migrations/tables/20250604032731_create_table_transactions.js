const SCHEMA = 'payment';
const TABLE_NAME = 'tbl_transactions';

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
    return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
        table.uuid('id').primary();
        table.string('gateway', 50).references('code').inTable(`${SCHEMA}.tbl_gateways`).notNullable();
        table.uuid('user_id').notNullable();
        table.string('gateway_transaction_id').notNullable();
        table.string('status', 20).notNullable();
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at');
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
