const SCHEMA = 'payment';
const TABLE_NAME = 'tbl_gateway_coupons';

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
    return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
        table.uuid('id').primary();
        table.string('gateway').notNullable();
        table.uuid('coupon_id').notNullable();
        table.string('gateway_coupon_id').notNullable();
        table.string('status').notNullable();
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at');
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
