const SCHEMA = 'payment';
const TABLE_NAME = 'tbl_methods';

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
    return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
        table.string('code', 32).primary();
        table.string('gateway', 32).references('code').inTable(`${SCHEMA}.tbl_gateways`).notNullable();
        table.string('label').notNullable();
        table.string('desc');
        table.string('status', 32).notNullable();
        table.boolean('visibility').notNullable();
        table.integer('sort_order').notNullable();
        table.json('platform_exclusion');
        table.json('source_type_exclusion');
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at');
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
