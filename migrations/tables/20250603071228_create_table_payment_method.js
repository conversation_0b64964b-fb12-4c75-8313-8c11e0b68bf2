const SCHEMA = 'payment';
const TABLE_NAME = 'tbl_payment_methods';

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
    return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
        table.uuid('id', 32).primary();
        table.string('gateway', 32).references('code').inTable(`${SCHEMA}.tbl_gateways`).notNullable();
        table.string('last4').notNullable();
        table.string('label').notNullable();
        table.string('reference').notNullable();
        table.boolean('is_default').notNullable();
        table.uuid('user_id').notNullable();
        table.string('type').notNullable();
        table.string('exp_month').notNullable();
        table.string('exp_year').notNullable();
        table.string('funding').notNullable();
        table.string('tenant_id').notNullable();
        table.boolean('archived').notNullable();
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at');
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
