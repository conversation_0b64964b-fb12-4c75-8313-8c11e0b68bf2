const SCHEMA = 'payment';
const TABLE_NAME = 'tbl_coupons';

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
    return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
        table.uuid('id').primary();
        table.string('name').notNullable();
        table.string('code').notNullable();
        table.string('status').nullable();
        table.string('duration').nullable();
        table.float('percent_off').nullable();
        table.integer('amount_off').nullable();
        table.bigInteger('effect_to').nullable();
        table.integer('max_redemptions').nullable();
        table.string('currency').nullable();
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at');
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
