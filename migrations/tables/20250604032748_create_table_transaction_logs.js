const SCHEMA = 'payment';
const TABLE_NAME = 'tbl_transaction_logs';

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
    return knex.schema.withSchema(SCHEMA).createTable(TABLE_NAME, (table) => {
        table.uuid('id').primary();
        table.uuid('transaction_id').notNullable();
        table.string('status', 20).notNullable();
        table.dateTime('created_at').notNullable();
        table.dateTime('updated_at');
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {};
