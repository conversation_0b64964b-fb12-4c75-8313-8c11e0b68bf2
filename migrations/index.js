// Migrations
const createTableGateways = require('./tables/20210904155511_create_table_gateways');
const createTableGatewayConfigs = require('./tables/20210904155533_create_table_gateway_configs');
const createTableGatewayCustomers = require('./tables/20210904156543_create_table_gateway_customers');
const createTableMethods = require('./tables/20210910073343_create_table_methods');
const createTableTransactions = require('./tables/20210910074211_create_table_transactions');
const createTableTransactionLogs = require('./tables/20210910074229_create_table_transaction_logs');
const createTableTransactionSources = require('./tables/20211221082006_create_table_transaction_sources');

exports.createTableGateways = createTableGateways;
exports.createTableGatewayConfigs = createTableGatewayConfigs;
exports.createTableMethods = createTableMethods;
exports.createTableTransactions = createTableTransactions;
exports.createTableTransactionLogs = createTableTransactionLogs;
exports.createTableTransactionSources = createTableTransactionSources;

exports.upAll = async (knex) => {
    await createTableGateways.up(knex);
    await createTableGatewayConfigs.up(knex);
    await createTableGatewayCustomers.up(knex);
    await createTableMethods.up(knex);
    await createTableTransactions.up(knex);
    await createTableTransactionLogs.up(knex);
    await createTableTransactionSources.up(knex);
};

exports.downAll = async (knex) => {
    await createTableTransactionSources.down(knex);
    await createTableTransactionLogs.down(knex);
    await createTableTransactions.down(knex);
    await createTableMethods.down(knex);
    await createTableGatewayCustomers.down(knex);
    await createTableGatewayConfigs.down(knex);
    await createTableGateways.down(knex);
};

// Seeds
const insertGateways = require('./seeds/insert_gateways');
const insertMethods = require('./seeds/insert_methods');

exports.insertGateways = insertGateways;
exports.insertMethods = insertMethods;

exports.seedAll = async (knex) => {
    await insertGateways.seed(knex);
    await insertMethods.seed(knex);
};
