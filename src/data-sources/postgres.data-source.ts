import { <PERSON><PERSON> } from "knex";
import { POSTGRES_DATA_SOURCE_CONFIG } from "@configs";
import { IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";
import { AbstractDataSourceClient, KnexClient } from "@heronjs/core";
import { Default, SQLError, SQLErrors, UseConfig } from "@heronjs/common";

@Default()
@UseConfig(POSTGRES_DATA_SOURCE_CONFIG)
export class PostgresDataSource extends AbstractDataSourceClient<KnexClient> implements IDatabase {
  constructor() {
    super();
  }

  getClient(): KnexClient {
    const client = this.database();
    if (!client) throw new SQLError(SQLErrors.CONNECTION_ERR, "Database client is undefined");
    return client;
  }

  async startTrx(): Promise<Knex.Transaction> {
    return this.getClient().transaction();
  }

  async commitTrx(trx: Knex.Transaction): Promise<void> {
    await trx.commit();
  }

  async rollbackTrx(trx: Knex.Transaction): Promise<void> {
    await trx.rollback();
  }

  async withTransaction<R>(
    operation: (trx: Knex.Transaction, client: KnexClient) => Promise<R>,
    options: RepositoryOptions = {},
  ): Promise<R> {
    const externalTrx = options?.trx;
    const client = this.getClient();
    const trx = externalTrx ?? (await client.transaction());

    try {
      const result = await operation(trx, client);
      if (!externalTrx) await trx.commit();
      return result;
    } catch (error) {
      if (!externalTrx) await trx.rollback();
      throw error;
    }
  }
}
