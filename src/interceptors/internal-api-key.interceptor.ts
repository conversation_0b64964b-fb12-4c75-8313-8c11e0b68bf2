import { Next, HttpRequest, HttpResponse, ExpressInterceptor } from "@heronjs/express";

export const InternalApiKeyInterceptor: ExpressInterceptor = (
  req: HttpRequest,
  res: HttpResponse,
  next: Next,
) => {
  const internalApiKey = req.headers["internal-api-key"];
  if (!internalApiKey || internalApiKey !== process.env.INTERNAL_API_KEY) {
    return res.status(401).send({
      message: "Unauthorized: Invalid or missing API key",
    });
  }
  return next();
};
