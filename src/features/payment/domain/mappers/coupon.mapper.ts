import { Lifecycle, Provider } from "@heronjs/common";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { CouponDto, GatewayCouponDto } from "@features/payment/domain/dtos";
import { Coupon, ICoupon } from "@features/payment/domain/aggregates/coupon/coupon";
import { GatewayCoupon } from "@features/payment/domain/aggregates/coupon/entities";

export type ICouponMapper = IMapper<CouponDto, ICoupon>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.MAPPER.COUPON,
  scope: Lifecycle.Singleton,
})
export class CouponMapper extends BaseMapper implements ICouponMapper {
  constructor() {
    super();
  }

  async fromEntityToDto(entity: ICoupon): Promise<CouponDto> {
    const gatewayCoupons: GatewayCouponDto[] = entity.gatewayCoupons.map((gc) => ({
      id: gc.id,
      gateway: gc.gateway,
      gatewayCouponId: gc.gatewayCouponId,
      status: gc.status,
      createdAt: gc.createdAt,
      updatedAt: gc.updatedAt,
    }));

    return {
      id: entity.id,
      name: entity.name,
      code: entity.code,
      status: entity.status,
      duration: entity.duration,
      percentOff: entity.percentOff,
      amountOff: entity.amountOff,
      effectTo: entity.effectTo,
      maxRedemptions: entity.maxRedemptions,
      currency: entity.currency,
      gatewayCoupons,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(dto: CouponDto): Promise<ICoupon> {
    const gatewayCoupons = dto.gatewayCoupons.map(
      (gcDto) =>
        new GatewayCoupon({
          id: gcDto.id,
          props: {
            id: gcDto.id,
            gateway: gcDto.gateway,
            gatewayCouponId: gcDto.gatewayCouponId,
            status: gcDto.status,
            createdAt: gcDto.createdAt,
            updatedAt: gcDto.updatedAt,
          },
        }),
    );

    return new Coupon({
      id: dto.id,
      props: {
        id: dto.id,
        name: dto.name,
        code: dto.code,
        status: dto.status,
        duration: dto.duration,
        percentOff: dto.percentOff,
        amountOff: dto.amountOff,
        effectTo: dto.effectTo,
        maxRedemptions: dto.maxRedemptions,
        currency: dto.currency,
        gatewayCoupons,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
