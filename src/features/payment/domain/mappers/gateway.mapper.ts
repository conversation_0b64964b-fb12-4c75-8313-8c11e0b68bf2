import { Lifecycle, Provider } from "@heronjs/common";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { GatewayDto } from "@features/payment/domain/dtos";
import { Gateway, IGateway } from "@features/payment/domain/aggregates/gateway/gateway";

export type IGatewayMapper = IMapper<GatewayDto, IGateway>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.MAPPER.GATEWAY,
  scope: Lifecycle.Singleton,
})
export class GatewayMapper extends BaseMapper implements IGatewayMapper {
  constructor() {
    super();
  }

  async fromEntityToDto(entity: IGateway): Promise<GatewayDto> {
    return {
      code: entity.code,
      label: entity.label,
      desc: entity.desc,
      status: entity.status,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(dto: GatewayDto): Promise<IGateway> {
    return new Gateway({
      id: dto.code,
      props: {
        code: dto.code,
        label: dto.label,
        desc: dto.desc,
        status: dto.status,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
