import { Lifecycle, Provider } from "@heronjs/common";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { WebhookEventDto } from "@features/payment/domain/dtos";
import { IWebhookEvent, WebhookEvent } from "@features/payment/domain/aggregates";

export type IWebhookEventMapper = IMapper<WebhookEventDto, IWebhookEvent>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.MAPPER.WEBHOOK_EVENT,
  scope: Lifecycle.Singleton,
})
export class WebhookEventMapper extends BaseMapper implements IWebhookEventMapper {
  constructor() {
    super();
  }

  async fromEntityToDto(entity: IWebhookEvent): Promise<WebhookEventDto> {
    return {
      id: entity.id,
      gateway: entity.gateway,
      referenceId: entity.referenceId,
      eventId: entity.eventId,
      eventType: entity.eventType,
      payload: entity.payload,
      errorMessage: entity.errorMessage,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(dto: WebhookEventDto): Promise<IWebhookEvent> {
    return new WebhookEvent({
      id: dto.id,
      props: {
        id: dto.id,
        gateway: dto.gateway,
        referenceId: dto.referenceId,
        eventId: dto.eventId,
        eventType: dto.eventType,
        payload: dto.payload,
        errorMessage: dto.errorMessage,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
