import { Lifecycle, Provider } from "@heronjs/common";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { GatewayConfigDto } from "@features/payment/domain/dtos";
import {
  GatewayConfig,
  IGatewayConfig,
} from "@features/payment/domain/aggregates/gateway-config/gateway-config";

export type IGatewayConfigMapper = IMapper<GatewayConfigDto, IGatewayConfig>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.MAPPER.GATEWAY_CONFIG,
  scope: Lifecycle.Singleton,
})
export class GatewayConfigMapper extends BaseMapper implements IGatewayConfigMapper {
  constructor() {
    super();
  }

  async fromEntityToDto(entity: IGatewayConfig): Promise<GatewayConfigDto> {
    return {
      code: entity.code,
      gatewayCode: entity.gatewayCode,
      label: entity.label,
      desc: entity.desc,
      value: entity.value,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(dto: GatewayConfigDto): Promise<IGatewayConfig> {
    return new GatewayConfig({
      id: dto.code,
      props: {
        code: dto.code,
        gatewayCode: dto.gatewayCode,
        label: dto.label,
        desc: dto.desc,
        value: dto.value,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
