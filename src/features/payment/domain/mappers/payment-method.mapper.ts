import { Lifecycle, Provider } from "@heronjs/common";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { BaseMapper, IMapper } from "@cbidigital/aqua-ddd";
import { PaymentMethodDto } from "@features/payment/domain/dtos";
import {
  PaymentMethod,
  IPaymentMethod,
} from "@features/payment/domain/aggregates/payment-method/payment-method";

export type IPaymentMethodMapper = IMapper<PaymentMethodDto, IPaymentMethod>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.MAPPER.PAYMENT_METHOD,
  scope: Lifecycle.Singleton,
})
export class PaymentMethodMapper extends Base<PERSON>apper implements IPaymentMethodMapper {
  constructor() {
    super();
  }

  async fromEntityToDto(entity: IPaymentMethod): Promise<PaymentMethodDto> {
    return {
      id: entity.id,
      gateway: entity.gateway,
      last4: entity.last4,
      label: entity.label,
      reference: entity.reference,
      isDefault: entity.isDefault,
      userId: entity.userId,
      type: entity.type,
      expMonth: entity.expMonth,
      expYear: entity.expYear,
      funding: entity.funding,
      tenantId: entity.tenantId,
      archived: entity.archived,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  async fromDtoToEntity(dto: PaymentMethodDto): Promise<IPaymentMethod> {
    return new PaymentMethod({
      id: dto.id,
      props: {
        id: dto.id,
        gateway: dto.gateway,
        last4: dto.last4,
        label: dto.label,
        reference: dto.reference,
        isDefault: dto.isDefault,
        userId: dto.userId,
        type: dto.type,
        expMonth: dto.expMonth,
        expYear: dto.expYear,
        funding: dto.funding,
        tenantId: dto.tenantId,
        archived: dto.archived,
        createdAt: dto.createdAt,
        updatedAt: dto.updatedAt,
      },
    });
  }
}
