import {
  AggregateRootBuilder,
  IAggregateRootBuilder,
  AggregateRootBuilderPayload,
} from "@cbidigital/aqua-ddd";
import { Lifecycle, Provider } from "@heronjs/common";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { ITransaction, Transaction } from "@features/payment/domain/aggregates";

export type TransactionBuilderBuildPayload = AggregateRootBuilderPayload<ITransaction>;
export type ITransactionBuilder = IAggregateRootBuilder<ITransaction>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.BUILDER.TRANSACTION,
  scope: Lifecycle.Singleton,
})
export class TransactionBuilder
  extends AggregateRootBuilder<ITransaction>
  implements ITransactionBuilder
{
  async build({
    id,
    props,
    externalProps,
  }: TransactionBuilderBuildPayload = {}): Promise<ITransaction> {
    return new Transaction({ id, props, externalProps });
  }
}
