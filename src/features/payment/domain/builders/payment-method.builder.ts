import {
  AggregateRootBuilder,
  IAggregateRootBuilder,
  AggregateRootBuilderPayload,
} from "@cbidigital/aqua-ddd";
import { Lifecycle, Provider } from "@heronjs/common";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import {
  PaymentMethod,
  IPaymentMethod,
} from "@features/payment/domain/aggregates/payment-method/payment-method";

export type PaymentMethodBuilderBuildPayload = AggregateRootBuilderPayload<IPaymentMethod>;
export type IPaymentMethodBuilder = IAggregateRootBuilder<IPaymentMethod>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.BUILDER.PAYMENT_METHOD,
  scope: Lifecycle.Singleton,
})
export class PaymentMethodBuilder
  extends AggregateRootBuilder<IPaymentMethod>
  implements IPaymentMethodBuilder
{
  async build({
    id,
    props,
    externalProps,
  }: PaymentMethodBuilderBuildPayload = {}): Promise<IPaymentMethod> {
    return new PaymentMethod({ id, props, externalProps });
  }
}
