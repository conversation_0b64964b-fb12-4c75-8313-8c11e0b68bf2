import {
  AggregateRootBuilder,
  IAggregateRootBuilder,
  AggregateRootBuilderPayload,
} from "@cbidigital/aqua-ddd";
import { Lifecycle, Provider } from "@heronjs/common";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import {
  GatewayCustomer,
  IGatewayCustomer,
} from "@features/payment/domain/aggregates/gateway-customer/gateway-customer";

export type GatewayCustomerBuilderBuildPayload = AggregateRootBuilderPayload<IGatewayCustomer>;
export type IGatewayCustomerBuilder = IAggregateRootBuilder<IGatewayCustomer>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.BUILDER.GATEWAY_CUSTOMER,
  scope: Lifecycle.Singleton,
})
export class GatewayCustomerBuilder
  extends AggregateRootBuilder<IGatewayCustomer>
  implements IGatewayCustomerBuilder
{
  async build({
    id,
    props,
    externalProps,
  }: GatewayCustomerBuilderBuildPayload = {}): Promise<IGatewayCustomer> {
    return new GatewayCustomer({ id, props, externalProps });
  }
}
