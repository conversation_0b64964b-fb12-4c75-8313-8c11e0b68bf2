import {
  WebhookeEventErrorCodes,
  WebhookEventErrorNamespaces,
} from "@features/payment/domain/aggregates/webhook-event/errors/errors";
import { RuntimeError } from "@heronjs/common";

export class WebhookEventAlreadyExistsError extends RuntimeError {
  constructor() {
    super(
      WebhookEventErrorNamespaces.WEBHOOK_EVENT,
      WebhookeEventErrorCodes.ALREADY_EXISTS,
      "Webhook event already exists.",
    );
  }
}
