import {
  WebhookeEventErrorCodes,
  WebhookEventErrorNamespaces,
} from "@features/payment/domain/aggregates/webhook-event/errors/errors";
import { RuntimeError } from "@heronjs/common";

export class WebhookEventNotFoundError extends RuntimeError {
  constructor() {
    super(
      WebhookEventErrorNamespaces.WEBHOOK_EVENT,
      WebhookeEventErrorCodes.NOT_FOUND,
      "Webhook event not found.",
    );
  }
}
