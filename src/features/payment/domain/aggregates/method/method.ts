import { Nullable } from "@heronjs/common";
import {
  AggregateRoot,
  AggregateRootConstructorPayload,
  IAggregateRoot,
} from "@cbidigital/aqua-ddd";
import { GatewayCodesEnum } from "@features/payment/domain/aggregates/gateway/enums";
import { MethodStatusEnum } from "@features/payment/domain/aggregates/method/enums";
import {
  CreateMethodInput,
  UpdateMethodInput,
} from "@features/payment/domain/aggregates/method/types";

export type MethodProps = {
  code: string;
  gateway: GatewayCodesEnum;
  label: string;
  desc: Nullable<string>;
  status: MethodStatusEnum;
  visibility: boolean;
  sortOrder: number;
  platformExclusion: Nullable<any>;
  sourceTypeExclusion: Nullable<any>;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type MethodMethods = {
  createMethod(payload: CreateMethodInput): Promise<void>;
  updateMethod(payload: UpdateMethodInput): void;
};

export type IMethod = IAggregateRoot<MethodProps, MethodMethods>;

export class Method extends AggregateRoot<MethodProps, MethodMethods> implements IMethod {
  static AGGREGATE_NAME = "method";

  constructor(payload: AggregateRootConstructorPayload<MethodProps>) {
    super(payload);
  }

  /** Properties **/

  get code(): string {
    return this.props.code;
  }

  get gateway(): GatewayCodesEnum {
    return this.props.gateway;
  }

  get label(): string {
    return this.props.label;
  }

  get desc(): Nullable<string> {
    return this.props.desc;
  }

  get status(): MethodStatusEnum {
    return this.props.status;
  }

  get visibility(): boolean {
    return this.props.visibility;
  }

  get sortOrder(): number {
    return this.props.sortOrder;
  }

  get platformExclusion(): Nullable<any> {
    return this.props.platformExclusion;
  }

  get sourceTypeExclusion(): Nullable<any> {
    return this.props.sourceTypeExclusion;
  }

  get createdAt(): Date {
    return this.props.createdAt;
  }

  get updatedAt(): Nullable<Date> {
    return this.props.updatedAt;
  }

  /** Methods **/

  private setCode(payload?: string): void {
    if (payload !== undefined) this.setProp("code", payload);
  }

  private setGateway(payload?: GatewayCodesEnum): void {
    if (payload !== undefined) this.setProp("gateway", payload);
  }

  private setLabel(payload?: string): void {
    if (payload !== undefined) this.setProp("label", payload);
  }

  private setDesc(payload?: Nullable<string>): void {
    if (payload !== undefined) this.setProp("desc", payload);
  }

  private setStatus(payload?: MethodStatusEnum): void {
    if (payload !== undefined) this.setProp("status", payload);
  }

  private setVisibility(payload?: boolean): void {
    if (payload !== undefined) this.setProp("visibility", payload);
  }

  private setSortOrder(payload?: number): void {
    if (payload !== undefined) this.setProp("sortOrder", payload);
  }

  private setPlatformExclusion(payload?: Nullable<any>): void {
    if (payload !== undefined) this.setProp("platformExclusion", payload);
  }

  private setSourceTypeExclusion(payload?: Nullable<any>): void {
    if (payload !== undefined) this.setProp("sourceTypeExclusion", payload);
  }

  private setCreatedAt(payload?: Date): void {
    if (payload !== undefined) this.setProp("createdAt", payload);
  }

  private setUpdatedAt(payload?: Date): void {
    if (payload !== undefined) this.setProp("updatedAt", payload);
  }

  async createMethod(payload: CreateMethodInput) {
    this.setCode(payload.code);
    this.setGateway(payload.gateway);
    this.setLabel(payload.label);
    this.setDesc(payload.desc);
    this.setStatus(payload.status);
    this.setVisibility(payload.visibility);
    this.setSortOrder(payload.sortOrder);
    this.setPlatformExclusion(payload.platformExclusion);
    this.setSourceTypeExclusion(payload.sourceTypeExclusion);
    this.setCreatedAt(new Date());
  }

  updateMethod(payload: UpdateMethodInput): void {
    this.setLabel(payload.label);
    this.setDesc(payload.desc);
    this.setStatus(payload.status);
    this.setVisibility(payload.visibility);
    this.setSortOrder(payload.sortOrder);
    this.setPlatformExclusion(payload.platformExclusion);
    this.setSourceTypeExclusion(payload.sourceTypeExclusion);
    this.setUpdatedAt(new Date());
  }
}
