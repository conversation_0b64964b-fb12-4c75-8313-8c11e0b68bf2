import {
  TransactionErrorCodes,
  TransactionErrorNamespaces,
} from "@features/payment/domain/aggregates/transaction/errors/errors";
import { RuntimeError } from "@heronjs/common";

export class TransactionAlreadyExistsError extends RuntimeError {
  constructor() {
    super(
      TransactionErrorNamespaces.TRANSACTION,
      TransactionErrorCodes.ALREADY_EXISTS,
      "Transaction already exists.",
    );
  }
}
