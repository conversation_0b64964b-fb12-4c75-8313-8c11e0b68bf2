import {
  PaymentMethodErrorCodes,
  PaymentMethodErrorNamespaces,
} from "@features/payment/domain/aggregates/payment-method/errors/errors";
import { RuntimeError } from "@heronjs/common";

export class PaymentMethodAlreadyExistsError extends RuntimeError {
  constructor() {
    super(
      PaymentMethodErrorNamespaces.PAYMENT_METHOD,
      PaymentMethodErrorCodes.ALREADY_EXISTS,
      "Payment method already exists.",
    );
  }
}
