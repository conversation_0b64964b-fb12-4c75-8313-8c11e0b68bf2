import {
  PaymentMethodErrorCodes,
  PaymentMethodErrorNamespaces,
} from "@features/payment/domain/aggregates/payment-method/errors/errors";
import { RuntimeError } from "@heronjs/common";

export class PaymentMethodMultipleDefaultsError extends RuntimeError {
  constructor() {
    super(
      PaymentMethodErrorNamespaces.PAYMENT_METHOD,
      PaymentMethodErrorCodes.MULTIPLE_DEFAULTS_NOT_ALLOWED,
      "User already has a default payment method. Please unset the existing default first.",
    );
  }
}
