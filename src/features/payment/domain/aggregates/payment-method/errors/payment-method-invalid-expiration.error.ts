import {
  PaymentMethodErrorCodes,
  PaymentMethodErrorNamespaces,
} from "@features/payment/domain/aggregates/payment-method/errors/errors";
import { RuntimeError } from "@heronjs/common";

export class PaymentMethodInvalidExpirationError extends RuntimeError {
  constructor() {
    super(
      PaymentMethodErrorNamespaces.PAYMENT_METHOD,
      PaymentMethodErrorCodes.INVALID_EXPIRATION_DATE,
      "Payment method expiration date cannot be in the past.",
    );
  }
}
