import {
  CouponErrorCodes,
  CouponErrorNamespaces,
} from "@features/payment/domain/aggregates/coupon/errors/errors";
import { RuntimeError } from "@heronjs/common";

export class CouponInvalidDiscountError extends RuntimeError {
  constructor() {
    super(
      CouponErrorNamespaces.COUPON,
      CouponErrorCodes.INVALID_DISCOUNT,
      "Coupon must have either percent_off or amount_off, but not both.",
    );
  }
}
