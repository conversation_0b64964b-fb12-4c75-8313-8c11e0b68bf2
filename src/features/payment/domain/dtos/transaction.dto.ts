import { Nullable } from "@heronjs/common";
import { TransactionStatusEnum } from "@features/payment/domain/aggregates/transaction/enums";

export type TransactionLogDto = {
  id: string;
  transactionId: string;
  status: TransactionStatusEnum;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type TransactionDto = {
  id: string;
  gateway: string;
  userId: string;
  gatewayTransactionId: string;
  status: TransactionStatusEnum;
  transactionLogs: TransactionLogDto[];
  createdAt: Date;
  updatedAt: Nullable<Date>;
};
