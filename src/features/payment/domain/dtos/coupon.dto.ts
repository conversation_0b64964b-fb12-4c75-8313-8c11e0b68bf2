import { Nullable } from "@heronjs/common";
import {
  CouponStatusEnum,
  CouponDurationEnum,
  GatewayCouponStatusEnum,
} from "@features/payment/domain/aggregates/coupon/enums";

export type GatewayCouponDto = {
  id: string;
  gateway: string;
  gatewayCouponId: string;
  status: GatewayCouponStatusEnum;
  createdAt: Date;
  updatedAt: Nullable<Date>;
};

export type CouponDto = {
  id: string;
  name: string;
  code: string;
  status: Nullable<CouponStatusEnum>;
  duration: Nullable<CouponDurationEnum>;
  percentOff: Nullable<number>;
  amountOff: Nullable<number>;
  effectTo: Nullable<number>;
  maxRedemptions: Nullable<number>;
  currency: Nullable<string>;
  gatewayCoupons: GatewayCouponDto[];
  createdAt: Date;
  updatedAt: Nullable<Date>;
};
