import z from "zod";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { IGatewayBuilder, IGatewayRepository } from "@features/payment/domain";
import { CreateGatewayInput } from "@features/payment/domain/aggregates/gateway/types";
import { GatewayAlreadyExistsError } from "@features/payment/domain/aggregates/gateway/errors";
import {
  GatewayCodesEnum,
  GatewayStatusEnum,
} from "@features/payment/domain/aggregates/gateway/enums";

export type CreateGatewayUseCaseInput = CreateGatewayInput;
export type CreateGatewayUseCaseOutput = { id: string };

const CreateGatewayUseCaseInputSchema = z.object({
  code: z.nativeEnum(GatewayCodesEnum),
  label: z.string(),
  desc: z.string().optional(),
  status: z.nativeEnum(GatewayStatusEnum),
});

export type ICreateGatewayUseCase = IUseCase<
  CreateGatewayUseCaseInput,
  CreateGatewayUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.USECASE.CREATE_GATEWAY,
  scope: Lifecycle.Transient,
})
export class CreateGatewayUseCase
  extends UseCase<CreateGatewayUseCaseInput, CreateGatewayUseCaseOutput, UseCaseContext>
  implements ICreateGatewayUseCase
{
  constructor(
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.GATEWAY)
    protected readonly builder: IGatewayBuilder,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.GATEWAY)
    protected readonly repo: IGatewayRepository,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: CreateGatewayUseCaseInput) => {
    const { code, label, desc, status } = CreateGatewayUseCaseInputSchema.parse(input);

    // Check if gateway with the same code already exists
    const existingGateway = await this.repo.findOne({
      filter: { code: { $eq: code } },
    });
    if (existingGateway) throw new GatewayAlreadyExistsError();

    // Create new gateway
    const gateway = await this.builder.build({ id: code });
    await gateway.createGateway({ code, label, desc, status });
    await this.repo.create(gateway);

    return { id: gateway.code };
  };
}
