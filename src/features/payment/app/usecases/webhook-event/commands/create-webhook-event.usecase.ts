import z from "zod";
import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { IWebhookEventBuilder, IWebhookEventRepository } from "@features/payment/domain";
import { CreateWebhookEventInput } from "@features/payment/domain/aggregates/webhook-event/types";
import { WebhookEventAlreadyExistsError } from "@features/payment/domain/aggregates/webhook-event/errors";

export type CreateWebhookEventUseCaseInput = CreateWebhookEventInput;
export type CreateWebhookEventUseCaseOutput = { id: string };

const CreateWebhookEventUseCaseInputSchema = z.object({
  gateway: z.string().min(1, "Gateway is required"),
  referenceId: z.string().min(1, "Reference ID is required"),
  eventId: z.string().min(1, "Event ID is required"),
  eventType: z.string().min(1, "Event type is required"),
  payload: z
    .record(z.any())
    .refine((data) => Object.keys(data).length > 0, "Payload cannot be empty"),
  errorMessage: z.string().nullable().optional(),
});

export type ICreateWebhookEventUseCase = IUseCase<
  CreateWebhookEventUseCaseInput,
  CreateWebhookEventUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.USECASE.CREATE_WEBHOOK_EVENT,
  scope: Lifecycle.Transient,
})
export class CreateWebhookEventUseCase
  extends UseCase<CreateWebhookEventUseCaseInput, CreateWebhookEventUseCaseOutput, UseCaseContext>
  implements ICreateWebhookEventUseCase
{
  constructor(
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.WEBHOOK_EVENT)
    protected readonly builder: IWebhookEventBuilder,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.WEBHOOK_EVENT)
    protected readonly repo: IWebhookEventRepository,
  ) {
    super();
    this.setMethods(this.processing);
  }

  processing = async (input: CreateWebhookEventUseCaseInput) => {
    const validatedInput = CreateWebhookEventUseCaseInputSchema.parse(input);

    // Check if webhook event with the same eventId already exists
    const existingWebhookEvent = await this.repo.findOne({
      filter: { eventId: { $eq: validatedInput.eventId } },
    });

    if (existingWebhookEvent) throw new WebhookEventAlreadyExistsError();

    // Create new webhook event entity
    const webhookEvent = await this.builder.build();
    await webhookEvent.create(validatedInput);

    // Save the webhook event
    await this.repo.create(webhookEvent);

    return { id: webhookEvent.id };
  };
}
