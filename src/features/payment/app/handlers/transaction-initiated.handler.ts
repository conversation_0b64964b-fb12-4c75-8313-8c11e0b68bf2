import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { IWebhookHandler } from "@features/payment/app/handlers/webhook.handler";
import {
  ID<PERSON><PERSON><PERSON><PERSON>,
  ITransactionBuilder,
  ITransactionRepository,
  IPaymentGatewayFactory,
  TransactionInitiatedEvent,
  TransactionAlreadyExistsError,
} from "@features/payment/domain";

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.HANDLER.TRANSACTION_INITIATED,
  scope: Lifecycle.Singleton,
})
export class TransactionInitiatedHandler implements IWebhookHandler {
  private readonly logger: ILogger;

  constructor(
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.TRANSACTION)
    protected readonly builder: ITransactionBuilder,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseU<PERSON>,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.TRANSACTION)
    protected readonly repo: ITransactionRepository,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
  ) {
    this.logger = new Logger(this.constructor.name);
  }

  async handle(event: TransactionInitiatedEvent) {
    const { data } = event;
    const { invoice } = data;
    this.logger.info("Started handling transaction initiated:::", invoice);
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      const isTransactionFound = await this.repo.findOne(
        { filter: { gatewayTransactionId: { $eq: invoice } } },
        repoOptions,
      );
      if (isTransactionFound) throw new TransactionAlreadyExistsError();
      const transaction = await this.builder.build();
      await transaction.create({
        gateway: event.gateway,
        userId: data.customer,
        gatewayTransactionId: invoice,
      });
      await this.repo.create(transaction, repoOptions);
      await trx.commit();
    } catch (error) {
      await trx.rollback();
      this.logger.error("Failed to handle transaction initiated", error);
      throw error;
    }
  }
}
