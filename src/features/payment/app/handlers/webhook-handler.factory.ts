import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { Inject, Lifecycle, Provider } from "@heronjs/common";
import { PAYMENT_EVENT_TYPE } from "@features/payment/domain";
import { IWebhookHandler } from "@features/payment/app/handlers/webhook.handler";

export interface IWebhookHandlerFactory {
  get(type: string): IWebhookHandler;
}

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.FACTORY.WEBHOOK_HANDLER,
  scope: Lifecycle.Singleton,
})
export class WebhookHandlerFactory implements IWebhookHandlerFactory {
  private readonly handlerMap: Array<{
    types: string[];
    handler: IWebhookHandler;
  }>;
  constructor(
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.HANDLER.TRANSACTION_INITIATED)
    private readonly transactionInitiatedHandler: IWeb<PERSON>ok<PERSON><PERSON><PERSON>,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.HANDLER.TRANSACTION_COMPLETED)
    private readonly transactionCompletedHandler: IWebhookHandler,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.HANDLER.TRANSACTION_FAILED)
    private readonly transactionFailedHandler: IWebhookHandler,
  ) {
    this.handlerMap = [
      {
        types: PAYMENT_EVENT_TYPE.TRANSACTION_INITIATED,
        handler: this.transactionInitiatedHandler,
      },
      {
        types: PAYMENT_EVENT_TYPE.TRANSACTION_COMPLETED,
        handler: this.transactionCompletedHandler,
      },
      {
        types: PAYMENT_EVENT_TYPE.TRANSACTION_FAILED,
        handler: this.transactionFailedHandler,
      },
    ];
  }

  get(type: string) {
    let handler = null;
    for (const entry of this.handlerMap) {
      if (entry.types.includes(type)) {
        handler = entry.handler;
        break;
      }
    }
    if (!handler) throw new Error("Webhook handler not found");
    return handler;
  }
}
