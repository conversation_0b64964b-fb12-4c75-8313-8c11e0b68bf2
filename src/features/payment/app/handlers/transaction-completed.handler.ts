import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { IWebhookHandler } from "@features/payment/app/handlers/webhook.handler";
import {
  ID<PERSON><PERSON><PERSON><PERSON>,
  ITransactionBuilder,
  ITransactionRepository,
  IPaymentGatewayFactory,
  TransactionNotFoundError,
  TransactionCompletedEvent,
} from "@features/payment/domain";

@Provider({
  token: PAYMENT_MODULE_INJECT_TOKENS.HANDLER.TRANSACTION_COMPLETED,
  scope: Lifecycle.Singleton,
})
export class TransactionCompletedHandler implements IWebhookHandler {
  private readonly logger: ILogger;

  constructor(
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.BUILDER.TRANSACTION)
    protected readonly builder: ITransactionBuilder,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.UTIL.DATABASE)
    protected readonly databaseUtil: IDatabaseUtil,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.TRANSACTION)
    protected readonly repo: ITransactionRepository,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.FACTORY.PAYMENT_GATEWAY)
    protected readonly paymentGatewayFactory: IPaymentGatewayFactory,
  ) {
    this.logger = new Logger(this.constructor.name);
  }

  async handle(event: TransactionCompletedEvent) {
    const { data } = event;
    const { invoice } = data;
    this.logger.info("Handling transaction completed:::", invoice);
    const trx = await this.databaseUtil.startTrx();
    const repoOptions = { trx };
    try {
      const transaction = await this.repo.findOne(
        { filter: { gatewayTransactionId: { $eq: invoice } } },
        repoOptions,
      );
      if (!transaction) throw new TransactionNotFoundError();
      transaction.markAsCompleted();
      await this.repo.update(transaction, repoOptions);
      await trx.commit();
    } catch (error) {
      await trx.rollback();
      this.logger.error("Failed to handle transaction completed", error);
      throw error;
    }
  }
}
