// import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
// import { IGetCouponByCodeUseCase } from "@features/payment/app";
// import { Get, Rest, Guard, Principal, Param } from "@heronjs/common";

// @Rest("/admin/coupons")
// export class AdminCouponRest {
//   @Get({ uri: "/:code" })
//   @Guard({ private: true })
//   async getList(@Principal("sub") authId: string, @Param("code") code: string) {
//     const useCase = <IGetCouponByCodeUseCase>(
//       use("Container").resolve(PAYMENT_MODULE_INJECT_TOKENS.USECASE.GET_COUPON_BY_CODE)
//     );

//     return useCase.exec({ code }, { auth: { authId } });
//   }
// }
