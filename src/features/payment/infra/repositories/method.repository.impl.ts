import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { IMethodDao } from "@features/payment/infra/database";
import { IMethodRepository } from "@features/payment/domain/repositories";
import { IMethod } from "@features/payment/domain/aggregates/method/method";
import { Inject, Optional, Lifecycle, Repository, DataSource } from "@heronjs/common";
import {
  IDatabase,
  QueryInput,
  BaseRepository,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import { MethodDto, IMethodMapper } from "@features/payment/domain";

@Repository({
  token: PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.METHOD,
  scope: Lifecycle.Singleton,
})
export class MethodRepository extends BaseRepository<IMethod> implements IMethodRepository {
  constructor(
    @DataSource() protected readonly db: IDatabase,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.DAO.METHOD)
    private readonly methodDao: IMethodDao,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.MAPPER.METHOD)
    private readonly mapper: IMethodMapper,
  ) {
    super({ db });
  }

  async create(entity: IMethod, options?: RepositoryOptions): Promise<IMethod> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.methodDao.create(dto, options);
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async update(entity: IMethod, options?: RepositoryOptions): Promise<IMethod> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.methodDao.update(dto, { ...options, trx });
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async delete(entity: IMethod, options?: RepositoryOptions): Promise<IMethod> {
    await this.withTransaction(async (trx) => {
      await this.methodDao.deleteById(entity.id, { ...options, trx });
      return entity;
    }, options);

    return entity;
  }

  async find(input: QueryInput, options?: RepositoryOptions): Promise<IMethod[]> {
    const dtos = await this.methodDao.find(input, options);
    const entities = await this.mapper.fromDtosToEntities(dtos as MethodDto[]);
    return entities;
  }

  async findOne(input: QueryInputFindOne, options?: RepositoryOptions): Promise<Optional<IMethod>> {
    const dto = await this.methodDao.findOne(input, options);
    const entity = dto ? await this.mapper.fromDtoToEntity(dto as MethodDto) : dto;
    return entity;
  }

  async upsertList(entities: IMethod[], options: RepositoryOptions) {
    const dtos = await this.mapper.fromEntitiesToDtos(entities);
    return entities;
  }

  async count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number> {
    return this.methodDao.count(input, options);
  }
}
