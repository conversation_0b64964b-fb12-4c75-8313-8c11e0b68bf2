import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { ICouponDao } from "@features/payment/infra/database";
import { ICouponRepository } from "@features/payment/domain/repositories";
import { ICoupon } from "@features/payment/domain/aggregates/coupon/coupon";
import { Inject, Optional, Lifecycle, Repository, DataSource } from "@heronjs/common";
import {
  IDatabase,
  QueryInput,
  BaseRepository,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import { CouponDto, ICouponMapper } from "@features/payment/domain";

@Repository({
  token: PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.COUPON,
  scope: Lifecycle.Singleton,
})
export class CouponRepository extends BaseRepository<ICoupon> implements ICouponRepository {
  constructor(
    @DataSource() protected readonly db: IDatabase,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.DAO.COUPON)
    private readonly couponDao: ICouponDao,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.MAPPER.COUPON)
    private readonly mapper: ICouponMapper,
  ) {
    super({ db });
  }

  async create(entity: ICoupon, options?: RepositoryOptions): Promise<ICoupon> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.couponDao.create(dto, options);
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async update(entity: ICoupon, options?: RepositoryOptions): Promise<ICoupon> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.couponDao.update(dto, { ...options, trx });
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async delete(entity: ICoupon, options?: RepositoryOptions): Promise<ICoupon> {
    await this.withTransaction(async (trx) => {
      await this.couponDao.deleteById(entity.id, { ...options, trx });
      return entity;
    }, options);

    return entity;
  }

  async find(input: QueryInput, options?: RepositoryOptions): Promise<ICoupon[]> {
    const dtos = await this.couponDao.find(input, options);
    const entities = await this.mapper.fromDtosToEntities(dtos as CouponDto[]);
    return entities;
  }

  async findOne(input: QueryInputFindOne, options?: RepositoryOptions): Promise<Optional<ICoupon>> {
    const dto = await this.couponDao.findOne(input, options);
    const entity = dto ? await this.mapper.fromDtoToEntity(dto as CouponDto) : dto;
    return entity;
  }

  async upsertList(entities: ICoupon[], options: RepositoryOptions) {
    const dtos = await this.mapper.fromEntitiesToDtos(entities);
    return entities;
  }

  async count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number> {
    return this.couponDao.count(input, options);
  }
}
