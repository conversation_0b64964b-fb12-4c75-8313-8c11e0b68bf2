import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { IGatewayDao } from "@features/payment/infra/database";
import { IGatewayRepository } from "@features/payment/domain/repositories";
import { IGateway } from "@features/payment/domain/aggregates/gateway/gateway";
import { Inject, Optional, Lifecycle, Repository, DataSource } from "@heronjs/common";
import {
  IDatabase,
  QueryInput,
  BaseRepository,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";
import { GatewayDto, IGatewayMapper } from "@features/payment/domain";

@Repository({
  token: PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.GATEWAY,
  scope: Lifecycle.Singleton,
})
export class GatewayRepository extends BaseRepository<IGateway> implements IGatewayRepository {
  constructor(
    @DataSource() protected readonly db: IDatabase,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.DAO.GATEWAY)
    private readonly gatewayDao: IGatewayDao,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.MAPPER.GATEWAY)
    private readonly mapper: IGatewayMapper,
  ) {
    super({ db });
  }

  async create(entity: IGateway, options?: RepositoryOptions): Promise<IGateway> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.gatewayDao.create(dto, options);
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async update(entity: IGateway, options?: RepositoryOptions): Promise<IGateway> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.gatewayDao.update(dto, { ...options, trx });
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async delete(entity: IGateway, options?: RepositoryOptions): Promise<IGateway> {
    await this.withTransaction(async (trx) => {
      await this.gatewayDao.deleteById(entity.id, { ...options, trx });
      return entity;
    }, options);

    return entity;
  }

  async find(input: QueryInput, options?: RepositoryOptions): Promise<IGateway[]> {
    const dtos = await this.gatewayDao.find(input, options);
    const entities = await this.mapper.fromDtosToEntities(dtos as GatewayDto[]);
    return entities;
  }

  async findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Optional<IGateway>> {
    const dto = await this.gatewayDao.findOne(input, options);
    const entity = dto ? await this.mapper.fromDtoToEntity(dto as GatewayDto) : dto;
    return entity;
  }

  async upsertList(entities: IGateway[], options: RepositoryOptions) {
    const dtos = await this.mapper.fromEntitiesToDtos(entities);
    return entities;
  }

  async count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number> {
    return this.gatewayDao.count(input, options);
  }
}
