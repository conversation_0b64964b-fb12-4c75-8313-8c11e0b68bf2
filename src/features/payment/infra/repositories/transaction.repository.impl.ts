import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { ITransactionDao } from "@features/payment/infra/database";
import { TransactionDto, ITransactionMapper } from "@features/payment/domain";
import { ITransactionRepository } from "@features/payment/domain/repositories";
import { Inject, Optional, Lifecycle, Repository, DataSource } from "@heronjs/common";
import { ITransaction } from "@features/payment/domain/aggregates/transaction/transaction";
import {
  IDatabase,
  QueryInput,
  BaseRepository,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";

@Repository({
  token: PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.TRANSACTION,
  scope: Lifecycle.Singleton,
})
export class TransactionRepository
  extends BaseRepository<ITransaction>
  implements ITransactionRepository
{
  constructor(
    @DataSource() protected readonly db: IDatabase,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.DAO.TRANSACTION)
    private readonly transactionDao: ITransactionDao,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.MAPPER.TRANSACTION)
    private readonly mapper: ITransactionMapper,
  ) {
    super({ db });
  }

  async create(entity: ITransaction, options?: RepositoryOptions): Promise<ITransaction> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.transactionDao.create(dto, options);
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async update(entity: ITransaction, options?: RepositoryOptions): Promise<ITransaction> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.transactionDao.update(dto, { ...options, trx });
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async delete(entity: ITransaction, options?: RepositoryOptions): Promise<ITransaction> {
    await this.withTransaction(async (trx) => {
      await this.transactionDao.deleteById(entity.id, { ...options, trx });
      return entity;
    }, options);

    return entity;
  }

  async find(input: QueryInput, options?: RepositoryOptions): Promise<ITransaction[]> {
    const dtos = await this.transactionDao.find(input, options);
    const entities = await this.mapper.fromDtosToEntities(dtos as TransactionDto[]);
    return entities;
  }

  async findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Optional<ITransaction>> {
    const dto = await this.transactionDao.findOne(input, options);
    const entity = dto ? await this.mapper.fromDtoToEntity(dto as TransactionDto) : dto;
    return entity;
  }

  async upsertList(entities: ITransaction[], options: RepositoryOptions) {
    const dtos = await this.mapper.fromEntitiesToDtos(entities);
    return entities;
  }

  async count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number> {
    return this.transactionDao.count(input, options);
  }
}
