import { PAYMENT_MODULE_INJECT_TOKENS } from "@shared";
import { IWebhookEventDao } from "@features/payment/infra/database";
import { WebhookEventDto, IWebhookEventMapper } from "@features/payment/domain";
import { IWebhookEventRepository } from "@features/payment/domain/repositories";
import { Inject, Optional, Lifecycle, Repository, DataSource } from "@heronjs/common";
import { IWebhookEvent } from "@features/payment/domain/aggregates/webhook-event/webhook-event";
import {
  IDatabase,
  QueryInput,
  BaseRepository,
  QueryInputFindOne,
  RepositoryOptions,
} from "@cbidigital/aqua-ddd";

@Repository({
  token: PAYMENT_MODULE_INJECT_TOKENS.REPOSITORY.WEBHOOK_EVENT,
  scope: Lifecycle.Singleton,
})
export class WebhookEventRepository
  extends BaseRepository<IWebhookEvent>
  implements IWebhookEventRepository
{
  constructor(
    @DataSource() protected readonly db: IDatabase,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.DAO.WEBHOOK_EVENT)
    private readonly webhookEventDao: IWebhookEventDao,
    @Inject(PAYMENT_MODULE_INJECT_TOKENS.MAPPER.WEBHOOK_EVENT)
    private readonly mapper: IWebhookEventMapper,
  ) {
    super({ db });
  }

  async create(entity: IWebhookEvent, options?: RepositoryOptions): Promise<IWebhookEvent> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.webhookEventDao.create(dto, options);
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async update(entity: IWebhookEvent, options?: RepositoryOptions): Promise<IWebhookEvent> {
    const trx = options?.trx ?? (await this.db.startTrx(options));
    const dto = await this.mapper.fromEntityToDto(entity);
    await this.webhookEventDao.update(dto, { ...options, trx });
    if (!options?.trx) await trx.commit();

    return entity;
  }

  async delete(entity: IWebhookEvent, options?: RepositoryOptions): Promise<IWebhookEvent> {
    await this.withTransaction(async (trx) => {
      await this.webhookEventDao.deleteById(entity.id, { ...options, trx });
      return entity;
    }, options);

    return entity;
  }

  async find(input: QueryInput, options?: RepositoryOptions): Promise<IWebhookEvent[]> {
    const dtos = await this.webhookEventDao.find(input, options);
    const entities = await this.mapper.fromDtosToEntities(dtos as WebhookEventDto[]);
    return entities;
  }

  async findOne(
    input: QueryInputFindOne,
    options?: RepositoryOptions,
  ): Promise<Optional<IWebhookEvent>> {
    const dto = await this.webhookEventDao.findOne(input, options);
    const entity = dto ? await this.mapper.fromDtoToEntity(dto as WebhookEventDto) : dto;
    return entity;
  }

  async upsertList(entities: IWebhookEvent[], options: RepositoryOptions) {
    const dtos = await this.mapper.fromEntitiesToDtos(entities);
    return entities;
  }

  async count(input: Pick<QueryInput, "filter">, options?: RepositoryOptions): Promise<number> {
    return this.webhookEventDao.count(input, options);
  }
}
