import { Nullable } from "@heronjs/common";
import { GatewayCodesEnum } from "@features/payment/domain/aggregates/gateway/enums";
import {
  PaymentMethodTypeEnum,
  PaymentMethodFundingEnum,
} from "@features/payment/domain/aggregates/payment-method/enums";

export type PaymentMethodRecord = {
  id: string;
  gateway: GatewayCodesEnum;
  last4: string;
  label: string;
  reference: string;
  is_default: boolean;
  user_id: string;
  type: PaymentMethodTypeEnum;
  exp_month: string;
  exp_year: string;
  funding: PaymentMethodFundingEnum;
  tenant_id: string;
  archived: boolean;
  created_at: Date;
  updated_at: Nullable<Date>;
};
