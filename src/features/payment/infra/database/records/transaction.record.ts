import { Nullable } from "@heronjs/common";
import { TransactionStatusEnum } from "@features/payment/domain/aggregates/transaction/enums";

export type TransactionLogRecord = {
  id: string;
  transaction_id: string;
  status: TransactionStatusEnum;
  created_at: Date;
  updated_at: Nullable<Date>;
};

export type TransactionRecord = {
  id: string;
  gateway: string;
  user_id: string;
  gateway_transaction_id: string;
  status: TransactionStatusEnum;
  created_at: Date;
  updated_at: Nullable<Date>;
};
