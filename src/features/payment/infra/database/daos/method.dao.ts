import { MethodDto } from "@features/payment/domain";
import { PAYMENT_MODULE_INJECT_TOKENS, TableNames } from "@shared";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { MethodRecord } from "@features/payment/infra/database/records";
import { MethodRecordMapper } from "@features/payment/infra/database/record-mappers";
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";

export interface IMethodDao extends IBaseDao<MethodDto, MethodRecord> {
  update(entity: Partial<MethodDto>, options?: RepositoryOptions): Promise<Partial<MethodDto>>;
  findByGateway(gateway: string, options?: RepositoryOptions): Promise<MethodDto[]>;
  findActiveByGateway(gateway: string, options?: RepositoryOptions): Promise<MethodDto[]>;
}

@Dao({
  token: PAYMENT_MODULE_INJECT_TOKENS.DAO.METHOD,
  scope: Lifecycle.Singleton,
})
export class MethodDao extends BaseDao<MethodDto, MethodRecord> implements IMethodDao {
  private readonly logger = new Logger(this.constructor.name);

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: TableNames.METHOD,
      recordMapper: new MethodRecordMapper(),
    });
  }

  async update(
    dto: Partial<MethodDto>,
    options: RepositoryOptions = {},
  ): Promise<Partial<MethodDto>> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto as MethodDto);
    const query = client.table(this.tableName).where("code", dto.code!).update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
    return dto;
  }

  async findByGateway(gateway: string, options: RepositoryOptions = {}): Promise<MethodDto[]> {
    const client = this.db.getClient();
    const query = client.table(this.tableName).where("gateway", gateway).orderBy("sort_order");
    if (options.trx) query.transacting(options.trx);
    const records = await query;
    return this.recordMapper.fromRecordsToDtos(records as MethodRecord[]) as MethodDto[];
  }

  async findActiveByGateway(
    gateway: string,
    options: RepositoryOptions = {},
  ): Promise<MethodDto[]> {
    const client = this.db.getClient();
    const query = client
      .table(this.tableName)
      .where("gateway", gateway)
      .where("status", "active")
      .where("visibility", true)
      .orderBy("sort_order");
    if (options.trx) query.transacting(options.trx);
    const records = await query;
    return this.recordMapper.fromRecordsToDtos(records as MethodRecord[]) as MethodDto[];
  }
}
