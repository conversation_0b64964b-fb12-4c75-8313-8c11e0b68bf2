import { GatewayDto } from "@features/payment/domain";
import { PAYMENT_MODULE_INJECT_TOKENS, TableNames } from "@shared";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { GatewayRecord } from "@features/payment/infra/database/records";
import { GatewayRecordMapper } from "@features/payment/infra/database/record-mappers";
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";

export interface IGatewayDao extends IBaseDao<GatewayDto, GatewayRecord> {
  update(entity: Partial<GatewayDto>, options?: RepositoryOptions): Promise<Partial<GatewayDto>>;
}

@Dao({
  token: PAYMENT_MODULE_INJECT_TOKENS.DAO.GATEWAY,
  scope: Lifecycle.Singleton,
})
export class GatewayDao extends BaseDao<GatewayDto, GatewayRecord> implements IGatewayDao {
  private readonly logger = new Logger(this.constructor.name);

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: TableNames.GATEWAY,
      recordMapper: new GatewayRecordMapper(),
    });
  }

  async update(dto: Partial<GatewayDto>, options: RepositoryOptions): Promise<Partial<GatewayDto>> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto);
    const query = client.table(this.tableName).where("code", dto.code!).update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
    return dto;
  }
}
