import { CouponDto, GatewayCouponDto } from "@features/payment/domain";
import { PAYMENT_MODULE_INJECT_TOKENS, TableNames } from "@shared";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { CouponRecord, GatewayCouponRecord } from "@features/payment/infra/database/records";
import {
  CouponRecordMapper,
  GatewayCouponRecordMapper,
} from "@features/payment/infra/database/record-mappers";
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";

export interface ICouponDao extends IBaseDao<CouponDto, CouponRecord> {
  update(entity: Partial<CouponDto>, options?: RepositoryOptions): Promise<Partial<CouponDto>>;
  findByCode(code: string, options?: RepositoryOptions): Promise<CouponDto | null>;
  findActiveByCode(code: string, options?: RepositoryOptions): Promise<CouponDto | null>;
  createGatewayCoupon(
    couponId: string,
    gatewayCoupon: GatewayCouponDto,
    options?: RepositoryOptions,
  ): Promise<void>;
  updateGatewayCoupon(
    couponId: string,
    gateway: string,
    gatewayCoupon: Partial<GatewayCouponDto>,
    options?: RepositoryOptions,
  ): Promise<void>;
  deleteGatewayCoupon(
    couponId: string,
    gateway: string,
    options?: RepositoryOptions,
  ): Promise<void>;
}

@Dao({
  token: PAYMENT_MODULE_INJECT_TOKENS.DAO.COUPON,
  scope: Lifecycle.Singleton,
})
export class CouponDao extends BaseDao<CouponDto, CouponRecord> implements ICouponDao {
  private readonly logger = new Logger(this.constructor.name);
  private readonly gatewayCouponMapper = new GatewayCouponRecordMapper();

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: TableNames.COUPON,
      recordMapper: new CouponRecordMapper(),
    });
  }

  async update(
    dto: Partial<CouponDto>,
    options: RepositoryOptions = {},
  ): Promise<Partial<CouponDto>> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto as CouponDto);
    const query = client.table(this.tableName).where("id", dto.id!).update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
    return dto;
  }

  async findByCode(code: string, options: RepositoryOptions = {}): Promise<CouponDto | null> {
    const client = this.db.getClient();
    const query = client.table(this.tableName).where("code", code).first();
    if (options.trx) query.transacting(options.trx);
    const record = await query;

    if (!record) return null;

    // Load gateway coupons
    const gatewayCouponsQuery = client
      .table(TableNames.GATEWAY_COUPON)
      .where("coupon_id", record.id);
    if (options.trx) gatewayCouponsQuery.transacting(options.trx);
    const gatewayCoupons = await gatewayCouponsQuery;

    return (this.recordMapper as any).fromRecordToDto(
      record as CouponRecord,
      gatewayCoupons as GatewayCouponRecord[],
    ) as CouponDto;
  }

  async findActiveByCode(code: string, options: RepositoryOptions = {}): Promise<CouponDto | null> {
    const client = this.db.getClient();
    const query = client
      .table(this.tableName)
      .where("code", code)
      .where("status", "active")
      .first();
    if (options.trx) query.transacting(options.trx);
    const record = await query;

    if (!record) return null;

    // Load gateway coupons
    const gatewayCouponsQuery = client
      .table(TableNames.GATEWAY_COUPON)
      .where("coupon_id", record.id);
    if (options.trx) gatewayCouponsQuery.transacting(options.trx);
    const gatewayCoupons = await gatewayCouponsQuery;

    return (this.recordMapper as any).fromRecordToDto(
      record as CouponRecord,
      gatewayCoupons as GatewayCouponRecord[],
    ) as CouponDto;
  }

  async createGatewayCoupon(
    couponId: string,
    gatewayCoupon: GatewayCouponDto,
    options: RepositoryOptions = {},
  ): Promise<void> {
    const client = this.db.getClient();
    const record = this.gatewayCouponMapper.fromDtoToRecord(gatewayCoupon);
    const query = client
      .table(TableNames.GATEWAY_COUPON)
      .insert({ ...record, coupon_id: couponId });
    if (options.trx) query.transacting(options.trx);
    await query;
  }

  async updateGatewayCoupon(
    couponId: string,
    gateway: string,
    gatewayCoupon: Partial<GatewayCouponDto>,
    options: RepositoryOptions = {},
  ): Promise<void> {
    const client = this.db.getClient();
    const record = this.gatewayCouponMapper.fromDtoToRecord(gatewayCoupon as GatewayCouponDto);
    const query = client
      .table(TableNames.GATEWAY_COUPON)
      .where("coupon_id", couponId)
      .where("gateway", gateway)
      .update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
  }

  async deleteGatewayCoupon(
    couponId: string,
    gateway: string,
    options: RepositoryOptions = {},
  ): Promise<void> {
    const client = this.db.getClient();
    const query = client
      .table(TableNames.GATEWAY_COUPON)
      .where("coupon_id", couponId)
      .where("gateway", gateway)
      .delete();
    if (options.trx) query.transacting(options.trx);
    await query;
  }
}
