import { GatewayConfigDto } from "@features/payment/domain";
import { PAYMENT_MODULE_INJECT_TOKENS, TableNames } from "@shared";
import { Dao, DataSource, Lifecycle, Logger } from "@heronjs/common";
import { GatewayConfigRecord } from "@features/payment/infra/database/records";
import { GatewayConfigRecordMapper } from "@features/payment/infra/database/record-mappers";
import { BaseDao, IBaseDao, IDatabase, RepositoryOptions } from "@cbidigital/aqua-ddd";

export interface IGatewayConfigDao extends IBaseDao<GatewayConfigDto, GatewayConfigRecord> {
  update(
    entity: Partial<GatewayConfigDto>,
    options?: RepositoryOptions,
  ): Promise<Partial<GatewayConfigDto>>;
  findByGatewayCode(gatewayCode: string, options?: RepositoryOptions): Promise<GatewayConfigDto[]>;
}

@Dao({
  token: PAYMENT_MODULE_INJECT_TOKENS.DAO.GATEWAY_CONFIG,
  scope: Lifecycle.Singleton,
})
export class GatewayConfigDao
  extends BaseDao<GatewayConfigDto, GatewayConfigRecord>
  implements IGatewayConfigDao
{
  private readonly logger = new Logger(this.constructor.name);

  constructor(@DataSource() db: IDatabase) {
    super({
      db,
      tableName: TableNames.GATEWAY_CONFIG,
      recordMapper: new GatewayConfigRecordMapper(),
    });
  }

  async update(
    dto: Partial<GatewayConfigDto>,
    options: RepositoryOptions = {},
  ): Promise<Partial<GatewayConfigDto>> {
    const client = this.db.getClient();
    const record = this.recordMapper.fromDtoToRecord(dto as GatewayConfigDto);
    const query = client.table(this.tableName).where("code", dto.code!).update(record);
    if (options.trx) query.transacting(options.trx);
    await query;
    return dto;
  }

  async findByGatewayCode(
    gatewayCode: string,
    options: RepositoryOptions = {},
  ): Promise<GatewayConfigDto[]> {
    const client = this.db.getClient();
    const query = client.table(this.tableName).where("gateway_code", gatewayCode);
    if (options.trx) query.transacting(options.trx);
    const records = await query;
    return this.recordMapper.fromRecordsToDtos(
      records as GatewayConfigRecord[],
    ) as GatewayConfigDto[];
  }
}
