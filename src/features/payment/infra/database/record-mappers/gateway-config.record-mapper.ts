import { IRecordMapper } from "@cbidigital/aqua-ddd";
import { GatewayConfigDto } from "@features/payment/domain";
import { GatewayConfigRecord } from "@features/payment/infra/database/records";

export class GatewayConfigRecordMapper
  implements IRecordMapper<GatewayConfigDto, GatewayConfigRecord>
{
  fromRecordToDto(record: GatewayConfigRecord): GatewayConfigDto {
    return {
      code: record.code,
      gatewayCode: record.gateway_code,
      label: record.label,
      desc: record.desc,
      value: record.value,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(dto: GatewayConfigDto): GatewayConfigRecord {
    return {
      code: dto.code,
      gateway_code: dto.gatewayCode,
      label: dto.label,
      desc: dto.desc,
      value: dto.value,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }

  fromRecordsToDtos(records: GatewayConfigRecord[]): GatewayConfigDto[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(dtos: GatewayConfigDto[]): GatewayConfigRecord[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}
