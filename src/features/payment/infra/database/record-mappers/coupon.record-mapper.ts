import { IRecordMapper } from "@cbidigital/aqua-ddd";
import { CouponDto, GatewayCouponDto } from "@features/payment/domain";
import { CouponRecord, GatewayCouponRecord } from "@features/payment/infra/database/records";

export class GatewayCouponRecordMapper
  implements IRecordMapper<GatewayCouponDto, GatewayCouponRecord>
{
  fromRecordToDto(record: GatewayCouponRecord): GatewayCouponDto {
    return {
      id: record.id,
      gateway: record.gateway,
      gatewayCouponId: record.gateway_coupon_id,
      status: record.status,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(dto: GatewayCouponDto): GatewayCouponRecord {
    return {
      id: dto.id,
      gateway: dto.gateway,
      gateway_coupon_id: dto.gatewayCouponId,
      status: dto.status,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }

  fromRecordsToDtos(records: GatewayCouponRecord[]): GatewayCouponDto[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(dtos: GatewayCouponDto[]): GatewayCouponRecord[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}

export class CouponRecordMapper implements IRecordMapper<CouponDto, CouponRecord> {
  private gatewayCouponMapper = new GatewayCouponRecordMapper();

  fromRecordToDto(record: CouponRecord, gatewayCoupons: GatewayCouponRecord[] = []): CouponDto {
    return {
      id: record.id,
      name: record.name,
      code: record.code,
      status: record.status,
      duration: record.duration,
      percentOff: record.percent_off,
      amountOff: record.amount_off,
      effectTo: record.effect_to,
      maxRedemptions: record.max_redemptions,
      currency: record.currency,
      gatewayCoupons: this.gatewayCouponMapper.fromRecordsToDtos(gatewayCoupons),
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(dto: CouponDto): CouponRecord {
    return {
      id: dto.id,
      name: dto.name,
      code: dto.code,
      status: dto.status,
      duration: dto.duration,
      percent_off: dto.percentOff,
      amount_off: dto.amountOff,
      effect_to: dto.effectTo,
      max_redemptions: dto.maxRedemptions,
      currency: dto.currency,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }

  fromRecordsToDtos(records: CouponRecord[]): CouponDto[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(dtos: CouponDto[]): CouponRecord[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}
