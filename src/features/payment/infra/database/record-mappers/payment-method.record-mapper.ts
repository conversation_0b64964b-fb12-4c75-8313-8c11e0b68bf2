import { IRecordMapper } from "@cbidigital/aqua-ddd";
import { PaymentMethodDto } from "@features/payment/domain";
import { PaymentMethodRecord } from "@features/payment/infra/database/records";

export class PaymentMethodRecordMapper
  implements IRecordMapper<PaymentMethodDto, PaymentMethodRecord>
{
  fromRecordToDto(record: PaymentMethodRecord): PaymentMethodDto {
    return {
      id: record.id,
      gateway: record.gateway,
      last4: record.last4,
      label: record.label,
      reference: record.reference,
      isDefault: record.is_default,
      userId: record.user_id,
      type: record.type,
      expMonth: record.exp_month,
      expYear: record.exp_year,
      funding: record.funding,
      tenantId: record.tenant_id,
      archived: record.archived,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(dto: PaymentMethodDto): PaymentMethodRecord {
    return {
      id: dto.id,
      gateway: dto.gateway,
      last4: dto.last4,
      label: dto.label,
      reference: dto.reference,
      is_default: dto.isDefault,
      user_id: dto.userId,
      type: dto.type,
      exp_month: dto.expMonth,
      exp_year: dto.expYear,
      funding: dto.funding,
      tenant_id: dto.tenantId,
      archived: dto.archived,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }

  fromRecordsToDtos(records: PaymentMethodRecord[]): PaymentMethodDto[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(dtos: PaymentMethodDto[]): PaymentMethodRecord[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}
