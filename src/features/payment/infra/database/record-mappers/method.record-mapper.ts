import { <PERSON><PERSON><PERSON>ordMapper } from "@cbidigital/aqua-ddd";
import { MethodDto } from "@features/payment/domain";
import { MethodRecord } from "@features/payment/infra/database/records";

export class MethodRecordMapper implements IRecordMapper<MethodDto, MethodRecord> {
  fromRecordToDto(record: MethodRecord): MethodDto {
    return {
      code: record.code,
      gateway: record.gateway,
      label: record.label,
      desc: record.desc,
      status: record.status,
      visibility: record.visibility,
      sortOrder: record.sort_order,
      platformExclusion: record.platform_exclusion,
      sourceTypeExclusion: record.source_type_exclusion,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(dto: MethodDto): MethodRecord {
    return {
      code: dto.code,
      gateway: dto.gateway,
      label: dto.label,
      desc: dto.desc,
      status: dto.status,
      visibility: dto.visibility,
      sort_order: dto.sortOrder,
      platform_exclusion: dto.platformExclusion,
      source_type_exclusion: dto.sourceTypeExclusion,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }

  fromRecordsToDtos(records: MethodRecord[]): MethodDto[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(dtos: MethodDto[]): MethodRecord[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}
