import { I<PERSON><PERSON>ordMapper } from "@cbidigital/aqua-ddd";
import { TransactionDto, TransactionLogDto } from "@features/payment/domain";
import { TransactionRecord, TransactionLogRecord } from "@features/payment/infra/database/records";

export class TransactionLogRecordMapper
  implements IRecordMapper<TransactionLogDto, TransactionLogRecord>
{
  fromRecordToDto(record: TransactionLogRecord): TransactionLogDto {
    return {
      id: record.id,
      transactionId: record.transaction_id,
      status: record.status,
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(dto: TransactionLogDto): TransactionLogRecord {
    return {
      id: dto.id,
      transaction_id: dto.transactionId,
      status: dto.status,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }

  fromRecordsToDtos(records: TransactionLogRecord[]): TransactionLogDto[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(dtos: TransactionLogDto[]): TransactionLogRecord[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}

export class TransactionRecordMapper implements IRecordMapper<TransactionDto, TransactionRecord> {
  private transactionLogMapper = new TransactionLogRecordMapper();

  fromRecordToDto(
    record: TransactionRecord,
    transactionLogs: TransactionLogRecord[] = [],
  ): TransactionDto {
    return {
      id: record.id,
      gateway: record.gateway,
      userId: record.user_id,
      gatewayTransactionId: record.gateway_transaction_id,
      status: record.status,
      transactionLogs: this.transactionLogMapper.fromRecordsToDtos(transactionLogs),
      createdAt: record.created_at,
      updatedAt: record.updated_at,
    };
  }

  fromDtoToRecord(dto: TransactionDto): TransactionRecord {
    return {
      id: dto.id,
      gateway: dto.gateway,
      user_id: dto.userId,
      gateway_transaction_id: dto.gatewayTransactionId,
      status: dto.status,
      created_at: dto.createdAt,
      updated_at: dto.updatedAt,
    };
  }

  fromRecordsToDtos(records: TransactionRecord[]): TransactionDto[] {
    return records.map((record) => this.fromRecordToDto(record));
  }

  fromDtosToRecords(dtos: TransactionDto[]): TransactionRecord[] {
    return dtos.map((dto) => this.fromDtoToRecord(dto));
  }
}
