volumes:
  payment_postgres_data:

networks:
  payment_network:

services:
  payment_service:
    image: node:latest
    working_dir: /usr/app
    volumes:
      - ./:/usr/app
    ports:
      - 3000:3000
      - 9229:9229
    env_file: .env
    depends_on:
      - payment_postgres
    command: sh -c "sleep 5 && npm run nodemon"
    networks:
      - payment_network

  payment_postgres:
    image: postgres
    environment:
      POSTGRES_USER: dev
      POSTGRES_PASSWORD: dev@123
    volumes:
      - payment_postgres_data:/var/lib/postgresql/data
    ports:
      - 5433:5432
    networks:
      - payment_network
